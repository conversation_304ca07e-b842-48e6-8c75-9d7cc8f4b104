from pydantic import BaseModel, Field
from typing import Optional, List
import base64
from datetime import datetime
import logging
import re
from typing import Dict
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_current_date_str() -> str:
    return datetime.now().strftime("%d/%m/%Y")

def transform_chat_to_expense(chat_response: Dict) -> Dict:
    """
    Detects if a chat response contains expense data in JSON format
    and transforms it into an ExpenseOutput structure.
    
    Args:
        chat_response: A dictionary containing the response from the chat endpoint
        
    Returns:
        A dictionary with the transformed response, including the appropriate response_type
    """
    try:
        # Make sure we have a chat response with reply field
        if chat_response.get("response_type") != "chat" or "data" not in chat_response:
            return chat_response
            
        chat_data = chat_response["data"]
        if "reply" not in chat_data:
            return chat_response
            
        reply_content = chat_data["reply"]
        
        # Extract JSON content from reply if it exists
        json_match = re.search(r'```json\n(.*?)\n```', reply_content, re.DOTALL)
        if not json_match:
            return chat_response
            
        json_str = json_match.group(1)
        expense_data = json.loads(json_str)
        
        # Validate if this is an ExpenseOutput structure
        required_fields = ["summary", "data"]
        if not all(field in expense_data for field in required_fields):
            logger.info("JSON found in chat reply, but it doesn't match ExpenseOutput structure")
            return chat_response
            
        # It looks like expense data! Transform the response
        # Extract a user-friendly message from the reply (text after the JSON block)
        message_text = reply_content.split("```", 2)[-1].strip()
        if not message_text:
            message_text = "I've processed your expense. Do you want to split this bill or need anything else with this expense?"
            
        # Create and validate the ExpenseOutput
        expense_output = ExpenseOutput(**expense_data)
            
        # Return transformed response
        return {
            "response_type": "expense",
            "data": expense_output.model_dump(),
            "message": message_text
        }
        
    except (json.JSONDecodeError, ValueError) as e:
        logger.warning(f"Failed to parse JSON from chat response: {e}")
        return chat_response
    except Exception as e:
        logger.exception(f"Error transforming chat response to expense: {e}")
        return chat_response
    

# --- Helper Functions ---
def encode_bytes_to_base64_data_uri(data: bytes, mime_type: str) -> str:
    """Encodes bytes to a base64 data URI."""
    base64_encoded_data = base64.b64encode(data).decode('utf-8')
    return f"data:{mime_type};base64,{base64_encoded_data}"

# --- Expense Pydantic Models ---
class PersonExpense(BaseModel):
    person_name: str = Field(description="Name of the person")
    amt: float | int = Field(description="Amount paid by this person or their share")

class ExpenseDetail(BaseModel):
    exp_name: str = Field(description="Name/type of the expense")
    exp_date: str = Field(description="Date of the expense in DD/MM/YYYY format")
    amount: float | int = Field(description="Total amount of the expense")
    items: str = Field(description="Description of items purchased")
    split_by: str = Field(description="How the expense was split")
    currency: str = Field(description="Currency used (e.g., INR, USD, EUR)")
    person: List[PersonExpense] = Field(description="People involved in this expense and their amounts/shares")

class ExpenseCollection(BaseModel):
    collection_name: str = Field(description="Name of the expense collection")
    expenses: List[ExpenseDetail] = Field(description="List of expenses in this collection")

class ExpenseOutput(BaseModel):
    summary: str = Field(description="A brief summary of the expenses")
    data: List[ExpenseCollection] = Field(description="List of expense collections")

class ExpenseTextInput(BaseModel):
    # This might not be directly used by the unified endpoint, but keep for potential internal use
    text: str = Field(..., description="Text describing the expenses to be processed.")
    collection_name: Optional[str] = Field(None, description="Optional override for the expense collection name")

# --- General Chat Pydantic Models ---
class ChatMessageInput(BaseModel):
    # This might be used internally if constructing history manually
    role: str = Field(..., description="Role of the message sender ('user' or 'assistant')")
    content: str = Field(..., description="The text content of the message")

class ChatInput(BaseModel):
    # Keep this, might be useful conceptually or if adapting input later
    user_id: str = Field(..., description="Unique identifier for the user or session")
    message: str = Field(..., description="The user's current message")

class ChatResponse(BaseModel):
    # Defines the structure of a chat reply
    reply: str = Field(..., description="The AI assistant's reply to the user")
    user_id: str = Field(..., description="Unique identifier for the user or session")