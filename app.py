import logging
from typing import Optional, List, Dict
from collections import defaultdict
from elevenlabs_integration import generate_audio_from_text, VoiceResponseData,elevenlabs_api_key,DEFAULT_VOICE_ID
import base64
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, status
from fastapi.responses import JSONResponse
from langchain_core.messages import BaseMessage
from langchain_core.output_parsers import PydanticOutputParser, StrOutputParser 
from functions.memory import m as memory

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from functions.funcs import (
        ExpenseOutput, ChatResponse
    )
    from functions.funcs import transform_chat_to_expense
    from functions.llm_functions import determine_query_type, process_expense_with_langchain, process_chat_with_langchain
    from elevenlabs_integration import elevenlabs_client
    logger.info("Successfully imported all required functions")
except ImportError as e:
    logger.error(f"Import error: {e}")
    try:
        from functions.funcs import (
            ExpenseOutput, ChatResponse,
        )
        from functions.funcs import transform_chat_to_expense
        from elevenlabs_integration import elevenlabs_client
        logger.warning("Partial import successful, but llm_functions may not be available")
    except ImportError as e2:
        logger.error(f"Critical import error: {e2}")
        raise

expense_parser = PydanticOutputParser(pydantic_object=ExpenseOutput)
string_parser = StrOutputParser()

# --- In-Memory Conversation History ---5
conversation_history: Dict[str, List[BaseMessage]] = defaultdict(list)
MAX_HISTORY_LENGTH = 10
last_expense_data: Dict[str, ExpenseOutput] = {}

app = FastAPI(
    title="Gemini Unified API",
    description="Single endpoint to process image/text for expense extraction OR handle general chat, with conversation memory and audio responses.",
    version="4.1.0"
)

@app.post(
    "/process-expense",
    summary="Process Input (Chat or Expense)",
    description="Unified endpoint. Provide text/image for processing. Routes automatically to chat or expense extraction using LLM intelligence.",
    tags=["Unified Processing"]
)
async def process_input(
    user_id: str = Form(..., description="Unique identifier for the user/session"),
    message: Optional[str] = Form(None, description="Text message for chat or expense description"),
    file: Optional[UploadFile] = File(None, description="Image file for expense processing (e.g., receipt)"),
    collection_name: Optional[str] = Form(None, description="Optional override for expense collection name (hints towards expense processing)"),
    voice_id: Optional[str] = Form(DEFAULT_VOICE_ID, description="ElevenLabs voice ID to use for audio generation"),
    generate_audio: bool = Form(True, description="Whether to generate audio for chat responses")
) -> JSONResponse:
    
    try:
        # Check if required functions are available
        if 'determine_query_type' not in globals():
            logger.error("determine_query_type function is not available")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Required LLM functions are not available. Please check server configuration."
            )
        
        # --- Input Validation and Routing Logic ---
        file_data: Optional[bytes] = None
        file_mime_type: Optional[str] = None
        is_image_input = False

        if file:
            file_data = await file.read()
            file_mime_type = file.content_type
            if file_mime_type and file_mime_type.startswith("image/"):
                logger.info(f"Processing image file provided by user {user_id}: {file.filename}")
                is_image_input = True
            else:
                # Received a file, but it's not an image - raise error
                logger.warning(f"User {user_id} uploaded a non-image file: {file.filename} ({file_mime_type})")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported file type: {file_mime_type}. Only image/* files are accepted for expense processing."
                )

        # Determine processing mode
        if is_image_input:
            # --- Route to Expense Processing (Image) ---
            logger.info(f"Routing user {user_id} request to IMAGE EXPENSE processing.")
            expense_result = await process_expense_with_langchain(
                user_id=user_id,
                file_data=file_data,
                file_mime_type=file_mime_type,
                text_data=message,
                collection_name=collection_name,
                input_type="image"
            )
            
            # Store expense processing in memory
            try:
                expense_memory_data = [
                    {"role": "user", "content": f"Processed expense from image: {message or 'Receipt uploaded'}"},
                    {"role": "assistant", "content": f"Extracted expense: {expense_result.summary}"}
                ]
                memory.add(expense_memory_data, user_id=user_id)
                logger.info(f"Stored expense processing in memory for user {user_id}")
            except Exception as e:
                logger.warning(f"Failed to store expense in memory: {e}")
            
            # Return structured expense data with a human-friendly message
            return JSONResponse(content={
                "response_type": "expense", 
                "data": expense_result.model_dump(),
                "message": "I've processed your expense details. Do you want to split this bill or need anything else with this expense?"
            })

        elif message:
            # --- Input is Text: Use LLM to classify query type ---
            query_type = await determine_query_type(user_id, message)
            
            if query_type == "expense" or query_type == "split" or collection_name:
                # If explicitly provided collection_name, also treat as expense regardless of LLM classification
                if query_type == "split":
                    # Process as expense with special handling for split requests
                    logger.info(f"Processing bill splitting request for user {user_id}: {message}")
                    try:
                        # Process split request using LLM directly
                        expense_result = await process_expense_with_langchain(
                            user_id=user_id,
                            text_data=message,
                            collection_name=collection_name or "Quick Split",
                            input_type="text"
                        )
                        
                        # Store split processing in memory
                        try:
                            split_memory_data = [
                                {"role": "user", "content": f"Split bill request: {message}"},
                                {"role": "assistant", "content": f"Created split for: {expense_result.summary}"}
                            ]
                            memory.add(split_memory_data, user_id=user_id)
                            logger.info(f"Stored split request in memory for user {user_id}")
                        except Exception as e:
                            logger.warning(f"Failed to store split in memory: {e}")
                        
                        # Return structured expense data
                        return JSONResponse(content={
                            "response_type": "expense", 
                            "data": expense_result.model_dump(),
                            "message": f"I've processed your expense splitting request. Each person should pay {expense_result.data[0].expenses[0].person[0].amt} {expense_result.data[0].expenses[0].currency}. Does this look correct?"
                        })
                    except Exception as e:
                        logger.exception(f"Error processing expense splitting for user {user_id}: {e}")
                        # Fall back to regular expense processing
                
                # --- Route to Expense Processing (Text) ---
                logger.info(f"Routing user {user_id} request to TEXT EXPENSE processing based on LLM classification.")
                expense_result = await process_expense_with_langchain(
                    user_id=user_id,
                    text_data=message,
                    collection_name=collection_name,
                    input_type="text"
                )
                
                # Store expense processing in memory
                try:
                    expense_memory_data = [
                        {"role": "user", "content": f"Expense entry: {message}"},
                        {"role": "assistant", "content": f"Processed expense: {expense_result.summary}"}
                    ]
                    memory.add(expense_memory_data, user_id=user_id)
                    logger.info(f"Stored expense processing in memory for user {user_id}")
                except Exception as e:
                    logger.warning(f"Failed to store expense in memory: {e}")
                
                # Return structured expense data
                return JSONResponse(content={
                    "response_type": "expense", 
                    "data": expense_result.model_dump(),
                    "message": "I've processed your expense. Do you want to split this bill or need anything else with this expense?"
                })
            else:
                # --- Route to General Chat ---
                logger.info(f"Routing user {user_id} request to GENERAL CHAT based on LLM classification.")
                chat_reply = await process_chat_with_langchain(
                    user_id=user_id,
                    user_message=message
                )
                
                # Store chat conversation in memory
                try:
                    chat_memory_data = [
                        {"role": "user", "content": message},
                        {"role": "assistant", "content": chat_reply}
                    ]
                    memory.add(chat_memory_data, user_id=user_id)
                    logger.info(f"Stored chat conversation in memory for user {user_id}")
                except Exception as e:
                    logger.warning(f"Failed to store chat in memory: {e}")
                
                # Create basic chat response
                chat_response_obj = ChatResponse(reply=chat_reply, user_id=user_id)
                response_data = {"response_type": "chat", "data": chat_response_obj.model_dump()}
                
                # Generate audio if requested and API key is available
                if generate_audio and elevenlabs_api_key:
                    try:
                        logger.info(f"Generating audio response for user {user_id}")
                        audio_data = await generate_audio_from_text(chat_reply, voice_id)
                        
                        if audio_data:
                            # Convert to base64
                            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                            
                            # Add audio to response
                            voice_response = VoiceResponseData(audio_base64=audio_base64)
                            response_data["audio"] = voice_response.model_dump()
                            logger.info(f"Added {len(audio_base64)} bytes of base64 audio to response")
                        else:
                            logger.warning("Audio generation failed, continuing without audio")
                    except Exception as e:
                        logger.exception(f"Error generating audio: {e}")
                        # Continue without audio if there's an error
                
                # Transform chat response if it contains expense JSON
                transformed_response = transform_chat_to_expense(response_data)
                
                # If transformed, update the user's expense data
                if transformed_response.get("response_type") == "expense" and "data" in transformed_response:
                    try:
                        # Update last_expense_data with the extracted expense info
                        expense_data = ExpenseOutput(**transformed_response["data"])
                        last_expense_data[user_id] = expense_data
                        logger.info(f"Updated expense data for user {user_id} from transformed chat response")
                    except Exception as e:
                        logger.warning(f"Failed to update last_expense_data from transformed response: {e}")
                
                return JSONResponse(content=transformed_response)
        else:
            # No image and no message provided
            logger.warning(f"User {user_id} made request to /process with no image or message.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No input provided. Please provide a 'message' or upload an image 'file'."
            )

    except HTTPException as http_exc:
        # Log and re-raise known HTTP exceptions
        logger.error(f"HTTP Exception for user {user_id} in /process: {http_exc.status_code} - {http_exc.detail}")
        raise http_exc
    except Exception as e:
        # Catch-all for unexpected errors
        logger.exception(f"An unexpected error occurred processing request for user {user_id}.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected server error occurred: {str(e)}"
        )
        
@app.post(
    "/clear-history",
    summary="Clear Conversation History",
    description="Delete conversation history and last expense data for a specific user",
    tags=["User Management"],
    status_code=status.HTTP_200_OK
)
async def clear_history(user_id: str = Form(..., description="Unique identifier for the user/session")):
    try:
        # Count history messages before clearing (for reporting)
        history_count = len(conversation_history.get(user_id, []))
        
        # Check if user exists in conversation history
        if user_id in conversation_history:
            # Clear conversation history
            conversation_history[user_id] = []
            logger.info(f"Cleared conversation history for user {user_id}")
        
        # Check if user has expense data
        had_expense_data = False
        if user_id in last_expense_data:
            # Clear last expense data
            del last_expense_data[user_id]
            had_expense_data = True
            logger.info(f"Cleared last expense data for user {user_id}")
        
        # Clear mem0 memories for the user
        try:
            memory.delete_all(user_id=user_id)
            logger.info(f"Cleared mem0 memories for user {user_id}")
        except Exception as e:
            logger.warning(f"Failed to clear mem0 memories for user {user_id}: {e}")
        
        return JSONResponse(
            content={
                "status": "success",
                "message": f"History cleared for user {user_id}",
                "details": {
                    "history_items_cleared": history_count,
                    "expense_data_cleared": had_expense_data,
                    "memory_cleared": True
                }
            }
        )
    except Exception as e:
        logger.exception(f"Error clearing history for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear history: {str(e)}"
        )

# --- Health Check Endpoint (Good Practice) ---
@app.get("/", summary="Health Check", tags=["System"])
async def root_health_check():
    return {
        "status": "ok",
        "features": {
            "audio": elevenlabs_api_key is not None,
            "memory": True
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app)