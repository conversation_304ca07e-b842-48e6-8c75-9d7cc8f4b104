# memory_helpers.py - Helper functions for better memory integration

import logging
from typing import List, Dict, Optional
from functions.memory import m as memory

logger = logging.getLogger(__name__)

def retrieve_context(query: str, user_id: str, limit: int = 5) -> List[Dict]:
    """
    Retrieve relevant context from Mem0 with better error handling and filtering
    """
    try:
        # Search for relevant memories
        search_results = memory.search(query=query, user_id=user_id, limit=limit)
        
        if not search_results or "results" not in search_results:
            logger.info(f"No memories found for user {user_id} with query: {query[:50]}...")
            return []
        
        # Filter and format memories
        relevant_memories = []
        for entry in search_results.get("results", []):
            if "memory" in entry and entry["memory"].strip():
                relevant_memories.append({
                    "memory": entry["memory"],
                    "score": entry.get("score", 0.0),
                    "created_at": entry.get("created_at", "")
                })
        
        logger.info(f"Retrieved {len(relevant_memories)} relevant memories for user {user_id}")
        return relevant_memories
        
    except Exception as e:
        logger.warning(f"Failed to retrieve memories for user {user_id}: {e}")
        return []

def format_memory_context(memories: List[Dict], max_memories: int = 3) -> str:
    """
    Format retrieved memories into a context string for prompts
    """
    if not memories:
        return "No previous context available."
    
    # Sort by relevance score and take top memories
    sorted_memories = sorted(memories, key=lambda x: x.get("score", 0), reverse=True)
    top_memories = sorted_memories[:max_memories]
    
    context_parts = []
    for i, memory_entry in enumerate(top_memories, 1):
        memory_text = memory_entry["memory"]
        context_parts.append(f"{i}. {memory_text}")
    
    return "\n".join(context_parts)

def save_interaction(user_id: str, user_input: str, assistant_response: str) -> bool:
    """
    Save user interaction to memory with improved context extraction
    """
    try:
        # Create structured interaction data
        interaction_data = [
            {
                "role": "user",
                "content": user_input
            },
            {
                "role": "assistant", 
                "content": assistant_response
            }
        ]
        
        # Add to memory
        memory.add(interaction_data, user_id=user_id)
        logger.info(f"Successfully saved interaction to memory for user {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save interaction to memory for user {user_id}: {e}")
        return False

def get_expense_context(user_id: str, expense_query: str) -> str:
    """
    Retrieve expense-specific context for better expense processing
    """
    try:
        # Search for expense-related memories
        expense_memories = retrieve_context(
            query=f"expense {expense_query}",
            user_id=user_id,
            limit=3
        )
        
        if not expense_memories:
            # Fallback to general expense patterns
            expense_memories = retrieve_context(
                query="expense bill payment split",
                user_id=user_id,
                limit=2
            )
        
        if expense_memories:
            context = format_memory_context(expense_memories)
            return f"User's expense patterns and preferences:\n{context}"
        else:
            return "No previous expense history available."
            
    except Exception as e:
        logger.warning(f"Failed to get expense context for user {user_id}: {e}")
        return "Unable to retrieve expense context."

def get_chat_context(user_id: str, current_message: str) -> str:
    """
    Retrieve chat-specific context for better conversational responses
    """
    try:
        # Get recent relevant conversations
        chat_memories = retrieve_context(
            query=current_message,
            user_id=user_id,
            limit=4
        )
        
        if chat_memories:
            context = format_memory_context(chat_memories, max_memories=3)
            return f"Previous relevant conversations:\n{context}"
        else:
            return "This appears to be a new conversation topic."
            
    except Exception as e:
        logger.warning(f"Failed to get chat context for user {user_id}: {e}")
        return "Unable to retrieve conversation context."

def update_user_preferences(user_id: str, preference_data: Dict) -> bool:
    """
    Update user preferences in memory
    """
    try:
        # Format preference as memory entry
        preference_text = f"User preference: {preference_data.get('type', 'general')} - {preference_data.get('value', '')}"
        
        memory.add([{
            "role": "system",
            "content": preference_text
        }], user_id=user_id)
        
        logger.info(f"Updated user preferences for {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to update preferences for user {user_id}: {e}")
        return False

def clear_user_memory(user_id: str) -> bool:
    """
    Clear all memories for a specific user
    """
    try:
        memory.delete_all(user_id=user_id)
        logger.info(f"Cleared all memories for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to clear memories for user {user_id}: {e}")
        return False