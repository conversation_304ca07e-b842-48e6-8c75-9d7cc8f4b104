import os
import logging
import re
import json
from typing import Optional, List, Dict, Union
from fastapi import HTTPException, status
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser, StrOutputParser
from functions.funcs import ExpenseOutput, encode_bytes_to_base64_data_uri, get_current_date_str
from functions.prompts import EXPENSE_SYSTEM_PROMPT_TEMPLATE, GENERAL_SYSTEM_PROMPT_TEMPLATE
from functions.memory_helper import (
    retrieve_context, format_memory_context, save_interaction,
    get_expense_context, get_chat_context
)
from pydantic import ValidationError
from langchain_core.exceptions import OutputParserException

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.error("GEMINI_API_KEY not found in environment variables.")
    raise ValueError("GEMINI_API_KEY environment variable not set")

# Initialize parsers here to avoid circular imports
expense_parser = PydanticOutputParser(pydantic_object=ExpenseOutput)
string_parser = StrOutputParser()

def get_llm_model():
    try:
        llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", google_api_key=api_key, temperature=0.0)
        logger.info(f"LangChain Google Generative AI model initialized successfully: {llm.model}")
        return llm
    except Exception as e:
        logger.exception("Failed to initialize LangChain Google Generative AI model.")
        
llm=get_llm_model()        


async def process_chat_with_langchain(
    user_id: str,
    user_message: str,
    conversation_history: Optional[Dict] = None,
    last_expense_data: Optional[Dict] = None
) -> str:
    """
    Enhanced chat processing with better memory integration
    """
    current_date_str = get_current_date_str()
    
    # Get contextual memories using helper function
    memory_context = get_chat_context(user_id, user_message)
    
    # Format system prompt with memory context
    system_prompt_text = GENERAL_SYSTEM_PROMPT_TEMPLATE.format(
        current_date=current_date_str,
        memory_context=memory_context
    )
    
    # Add expense context if user has recent expense data
    if last_expense_data and user_id in last_expense_data:
        expense_data = last_expense_data[user_id].model_dump()
        expense_context = f"\n\nRecent expense context:\n```json\n{json.dumps(expense_data, indent=2)}\n```\nUser may be asking about this expense or wanting to split it."
        system_prompt_text += expense_context
    
    system_message = SystemMessage(content=system_prompt_text)
    user_history = conversation_history.get(user_id, []) if conversation_history else []
    human_message = HumanMessage(content=user_message)
    messages = [system_message] + user_history[-5:] + [human_message]  # Limit history to last 5 exchanges

    logger.info(f"Sending enhanced chat request to Gemini for user {user_id}")
    try:
        response = await llm.ainvoke(messages)
        parsed_output = string_parser.invoke(response.content)
        
        # Save interaction to memory for future context
        save_interaction(user_id, user_message, parsed_output)
        
        logger.info(f"Processed chat response with memory integration for user {user_id}")
        return parsed_output
        
    except Exception as e:
        logger.exception(f"Error in enhanced chat processing for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing chat with AI model: {str(e)}"
        )

async def process_expense_with_langchain(
    user_id: str,
    file_data: Optional[bytes] = None,
    file_mime_type: Optional[str] = None,
    text_data: Optional[str] = None,
    collection_name: Optional[str] = None,
    input_type: str = "unknown",
    is_split_request: bool = False,
    conversation_history: Optional[Dict] = None
) -> ExpenseOutput:
    """
    Enhanced expense processing with better memory context integration
    """
    current_date_str = get_current_date_str()
    format_instructions = expense_parser.get_format_instructions()
    
    # Get expense-specific context using helper function
    query_text = text_data or "expense processing"
    memory_context = get_expense_context(user_id, query_text)
    
    # Determine if this is a split request
    split_mode = is_split_request or (await determine_query_type(user_id, text_data or "") == "split")
    
    # Use enhanced prompt template with memory context
    system_prompt_text = EXPENSE_SYSTEM_PROMPT_TEMPLATE.format(
        format_instructions=format_instructions,
        current_date=current_date_str,
        memory_context=memory_context
    )
    
    # Add split-specific instructions if needed
    if split_mode:
        split_instructions = """
        
        **Split Request Processing:**
        - Focus on extracting names of people involved
        - Identify total amount and split method (equal by default)
        - Use user's established patterns for person naming
        - Apply consistent currency preferences from memory
        """
        system_prompt_text += split_instructions

    # Add collection name override if provided
    if collection_name:
        collection_override = f"\n\n**Collection Override:** Use '{collection_name}' as the collection_name for all expenses."
        system_prompt_text += collection_override
        logger.info(f"Using collection name override: {collection_name}")

    system_message = SystemMessage(content=system_prompt_text)
    
    # Prepare message content based on input type
    human_message_content = []
    if input_type == "image" and file_data and file_mime_type:
        data_uri = encode_bytes_to_base64_data_uri(file_data, file_mime_type)
        human_message_content = [
            {"type": "text", "text": "Extract expense details from this image using my established patterns:"},
            {"type": "image_url", "image_url": {"url": data_uri}},
        ]
    elif input_type == "text" and text_data:
        human_message_content = [
            {"type": "text", "text": f"Extract expense details from this text using my established patterns:\n\n{text_data}"}
        ]
    else:
        logger.error(f"Invalid input for expense processing: type={input_type}")
        raise ValueError("Invalid input for expense processing function.")

    human_message = HumanMessage(content=human_message_content)
    
    # Include limited conversation history for context
    user_history = conversation_history.get(user_id, [])[-3:] if conversation_history else []
    messages = [system_message] + user_history + [human_message]

    process_type = "split request" if split_mode else f"{input_type} expense"
    logger.info(f"Processing {process_type} with memory context for user {user_id}")
    
    try:
        response = await llm.ainvoke(messages)
        parsed_output: ExpenseOutput = expense_parser.invoke(response.content)
        
        # Save expense interaction to memory
        expense_summary = f"Processed {process_type}: {parsed_output.summary}"
        save_interaction(user_id, text_data or "Image expense", expense_summary)
        
        logger.info(f"Successfully processed {process_type} with memory integration for user {user_id}")
        return parsed_output
        
    except (OutputParserException, ValidationError, json.JSONDecodeError) as parse_error:
        logger.error(f"Failed to parse {process_type} response for user {user_id}: {parse_error}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse AI response for {process_type}: {str(parse_error)}"
        )
    except Exception as e:
        logger.exception(f"Error in {process_type} processing for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing {process_type}: {str(e)}"
        )

async def determine_query_type(user_id: str, message: str) -> str:
    """
    Enhanced query classification with memory context
    """
    # Get user's typical query patterns from memory
    query_memories = retrieve_context(message, user_id, limit=2)
    memory_context = ""
    
    if query_memories:
        memory_context = f"\n\nUser's typical patterns:\n{format_memory_context(query_memories, max_memories=2)}"

    system_prompt = f"""
    You are classifying user queries for a financial expense app. Based on the message and user patterns, classify as:
    
    - "expense": Recording, tracking, or managing expenses/receipts/bills
    - "split": Specifically splitting bills/expenses between people  
    - "chat": General conversation not related to expenses
    
    User Message: {message}
    {memory_context}
    
    Respond with exactly one word: "expense", "split", or "chat"
    """
    
    system_message = SystemMessage(content=system_prompt)
    human_message = HumanMessage(content=message)
    messages = [system_message, human_message]
    
    try:
        response = await llm.ainvoke(messages)
        result = response.content.strip().lower()
        
        # Normalize result
        if "expense" in result:
            result = "expense"
        elif "split" in result:
            result = "split"
        else:
            result = "chat"
        
        logger.info(f"Query classified as '{result}' for user {user_id}")
        return result
        
    except Exception as e:
        logger.exception(f"Error in query classification for user {user_id}: {e}")
        return "chat"  # Default to chat on err