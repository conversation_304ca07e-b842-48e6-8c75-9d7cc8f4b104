name: Deploy Splitpoint AI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  deploy:
    if: github.event_name != 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Debug SSH key (optional)
        run: |
          if [ -n "${{ secrets.SERVER_SSH_KEY }}" ]; then
            echo "SSH key is set"
          else
            echo "SSH key is NOT set"
          fi
        shell: bash

      - name: Deploy to server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: 22
          script_stop: true
          script: |
            echo "Wait and watch!"
            cd /root/projects/splitpoint-ai

            # Configure git to use SSH instead of HTTPS (do this once)
            if git remote -v | grep -q 'https://'; then
              echo "Switching remote from HTTPS to SSH"
              git remote set-<NAME_EMAIL>:OnPoint-Software-Services/splitpoint-ai-backend.git
            fi

            echo "updating the deployment"
            git fetch origin
            git reset --hard origin/main # Or use git pull origin main
            git pull origin main 

            echo "building docker image"
            docker build -t splitpoint-ai .

            echo "stopping & removing container"
            docker stop splitpoint-container || true && docker rm splitpoint-container || true

            echo "starting new container"
            docker run -p 3001:3001 -d --name splitpoint-container splitpoint-ai

            echo "The application has been deployed successfully."
            docker logs splitpoint-container
