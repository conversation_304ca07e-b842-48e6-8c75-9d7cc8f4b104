import os
import logging
import requests
import base64
from typing import Optional, Dict, Any, Tu<PERSON>
from dotenv import load_dotenv
import aiohttp
from functions.funcs import ChatResponse
from pydantic import BaseModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")

class ElevenLabsClient:
    """Client for interacting with the ElevenLabs Text-to-Speech API."""
    
    def __init__(self):
        """Initialize the ElevenLabs client with API key from environment variables."""
        self.api_key = os.getenv("ELEVENLABS_API_KEY")
        if not self.api_key:
            logger.warning("ELEVENLABS_API_KEY not found in environment variables.")
        
        self.base_url = "https://api.elevenlabs.io/v1"
        self.default_voice_id = os.getenv("ELEVENLABS_DEFAULT_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")  # Default voice ID (ElevenLabs' "Rachel")
        self.model_id = os.getenv("ELEVENLABS_MODEL_ID", "eleven_monolingual_v1")  # Default model
    
    def text_to_speech(self, text: str, voice_id: Optional[str] = None) -> Tuple[Optional[bytes], Optional[str]]:
        """
        Convert text to speech using ElevenLabs API.
        
        Args:
            text: The text to convert to speech
            voice_id: Optional voice ID to use (defaults to the configured default voice)
            
        Returns:
            Tuple containing:
                - Audio data as bytes (or None if error)
                - Base64 encoded audio data (or None if error)
        """
        if not self.api_key:
            logger.error("Cannot use ElevenLabs API: No API key configured")
            return None, None
            
        voice_id = voice_id or self.default_voice_id
        url = f"{self.base_url}/text-to-speech/{voice_id}"
        
        # Prepare the request
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": self.api_key
        }
        
        # Truncate text if it's too long (ElevenLabs has a character limit)
        if len(text) > 5000:  # Typical limit is 5000 chars
            logger.warning(f"Text too long ({len(text)} chars), truncating to 5000 chars")
            text = text[:4997] + "..."
        
        data = {
            "text": text,
            "model_id": self.model_id,
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }
        
        try:
            logger.info(f"Sending TTS request to ElevenLabs for {len(text)} chars of text")
            response = requests.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                audio_data = response.content
                # Convert to base64 for embedding in responses
                base64_audio = base64.b64encode(audio_data).decode('utf-8')
                logger.info(f"Successfully generated audio, size: {len(audio_data)} bytes")
                return audio_data, base64_audio
            else:
                logger.error(f"ElevenLabs API error: {response.status_code} - {response.text}")
                return None, None
                
        except Exception as e:
            logger.exception(f"Error in ElevenLabs TTS request: {e}")
            return None, None
    
    def get_available_voices(self) -> Dict[str, Any]:
        """Get the list of available voices from ElevenLabs."""
        if not self.api_key:
            logger.error("Cannot use ElevenLabs API: No API key configured")
            return {"error": "No API key configured"}
            
        url = f"{self.base_url}/voices"
        
        headers = {
            "Accept": "application/json",
            "xi-api-key": self.api_key
        }
        
        try:
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"ElevenLabs API error: {response.status_code} - {response.text}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            logger.exception(f"Error in ElevenLabs voices request: {e}")
            return {"error": str(e)}

# Create singleton instance
elevenlabs_client = ElevenLabsClient()

# --- ElevenLabs API Setup ---
elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
if not elevenlabs_api_key:
    logger.warning("ELEVENLABS_API_KEY not found in environment variables. Audio generation will not be available.")

ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1/text-to-speech"
DEFAULT_VOICE_ID = os.getenv("ELEVENLABS_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")  # Default to "Rachel" voice if not specified

# ElevenLabs model parameters
ELEVENLABS_MODEL_ID = os.getenv("ELEVENLABS_MODEL_ID", "eleven_monolingual_v1")
ELEVENLABS_STABILITY = float(os.getenv("ELEVENLABS_STABILITY", "0.5"))
ELEVENLABS_SIMILARITY = float(os.getenv("ELEVENLABS_SIMILARITY", "0.75"))
ELEVENLABS_STYLE = float(os.getenv("ELEVENLABS_STYLE", "0.0"))
ELEVENLABS_SPEAKER_BOOST = os.getenv("ELEVENLABS_SPEAKER_BOOST", "true").lower() == "true"

class VoiceResponseData(BaseModel):
    audio_base64: str
    content_type: str = "audio/mpeg"

class ChatResponseWithAudio(ChatResponse):
    audio: Optional[VoiceResponseData] = None

# --- ElevenLabs API Functions ---
async def generate_audio_from_text(text: str, voice_id: str = DEFAULT_VOICE_ID) -> Optional[bytes]:
    """Generate audio from text using ElevenLabs API."""
    if not elevenlabs_api_key:
        logger.warning("ElevenLabs API key not set. Skipping audio generation.")
        return None
    
    # Prepare the request payload
    payload = {
        "text": text,
        "model_id": ELEVENLABS_MODEL_ID,
        "voice_settings": {
            "stability": ELEVENLABS_STABILITY,
            "similarity_boost": ELEVENLABS_SIMILARITY,
            "style": ELEVENLABS_STYLE,
            "use_speaker_boost": ELEVENLABS_SPEAKER_BOOST
        }
    }
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": elevenlabs_api_key
    }
    
    url = f"{ELEVENLABS_API_URL}/{voice_id}"
    
    try:
        async with aiohttp.ClientSession() as session:
            logger.info(f"Sending request to ElevenLabs API with text length: {len(text)}")
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    audio_data = await response.read()
                    logger.info(f"Successfully generated audio: {len(audio_data)} bytes")
                    return audio_data
                else:
                    error_text = await response.text()
                    logger.error(f"ElevenLabs API error: {response.status} - {error_text}")
                    return None
    except Exception as e:
        logger.exception(f"Error generating audio with ElevenLabs API: {e}")
        return None
