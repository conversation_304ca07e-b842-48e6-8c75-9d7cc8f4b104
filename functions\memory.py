from mem0 import Memory
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv("GEMINI_API_KEY")

config = {
    "llm": {
        "provider": "gemini",
        "config": {
            "model": "gemini-2.0-flash",
            "temperature": 0.0,
            "api_key": api_key
        }
    },
    "vector_store": {
        "provider": "pgvector",
        "config": {
            "host": os.getenv("PGVECTOR_HOST"),
            "port": os.getenv("PGVECTOR_PORT"),
            "dbname": os.getenv("PGVECTOR_DB"),  
            "user": os.getenv("PGVECTOR_USER"),
            "password": os.getenv("PGVECTOR_PASSWORD")
        }
    },
    "embedder": {
        "provider": "gemini",
        "config": {
            "model": "models/gemini-embedding-exp-03-07",
            "embedding_dims": 1536,
            "api_key": api_key
        }
    }
}

m = Memory.from_config(config)